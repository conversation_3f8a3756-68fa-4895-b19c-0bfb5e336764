"""
Complete system verification script
This will test all components to ensure they work correctly
"""

import os
import sys

def test_imports():
    """Test if all modules can be imported"""
    print("=== Testing Module Imports ===")
    
    try:
        import Recognition_Model
        print("✓ Recognition_Model imported")
    except Exception as e:
        print(f"✗ Recognition_Model failed: {e}")
        return False
    
    try:
        import Email_Alerts
        print("✓ Email_Alerts imported")
    except Exception as e:
        print(f"✗ Email_Alerts failed: {e}")
        return False
    
    try:
        import Face_Features
        print("✓ Face_Features imported")
    except Exception as e:
        print(f"✗ Face_Features failed: {e}")
        return False
    
    try:
        import Face_Dataset
        print("✓ Face_Dataset imported")
    except Exception as e:
        print(f"✗ Face_Dataset failed: {e}")
        return False
    
    return True

def test_training():
    """Test model training"""
    print("\n=== Testing Model Training ===")
    
    if not os.path.exists('dataset'):
        print("✗ No dataset found. Please create a dataset first.")
        return False
    
    # Count people in dataset
    people = [d for d in os.listdir('dataset') if os.path.isdir(os.path.join('dataset', d))]
    print(f"Found {len(people)} people in dataset: {people}")
    
    if len(people) == 0:
        print("✗ No people found in dataset.")
        return False
    
    try:
        from Recognition_Model import train_model
        
        # Backup existing model
        if os.path.exists('face_recognition_model.pkl'):
            os.rename('face_recognition_model.pkl', 'face_recognition_model_backup.pkl')
            print("Backed up existing model")
        
        print("Training model...")
        success = train_model()
        
        if success and os.path.exists('face_recognition_model.pkl'):
            print("✓ Model training successful!")
            
            # Clean up backup
            if os.path.exists('face_recognition_model_backup.pkl'):
                os.remove('face_recognition_model_backup.pkl')
            
            return True
        else:
            print("✗ Model training failed!")
            
            # Restore backup
            if os.path.exists('face_recognition_model_backup.pkl'):
                os.rename('face_recognition_model_backup.pkl', 'face_recognition_model.pkl')
                print("Restored backup model")
            
            return False
            
    except Exception as e:
        print(f"✗ Training error: {e}")
        
        # Restore backup on error
        if os.path.exists('face_recognition_model_backup.pkl'):
            os.rename('face_recognition_model_backup.pkl', 'face_recognition_model.pkl')
            print("Restored backup model")
        
        return False

def test_model_loading():
    """Test if the trained model can be loaded"""
    print("\n=== Testing Model Loading ===")
    
    if not os.path.exists('face_recognition_model.pkl'):
        print("✗ No trained model found.")
        return False
    
    try:
        import joblib
        model = joblib.load('face_recognition_model.pkl')
        print("✓ Model loaded successfully!")
        
        # Test model structure
        if hasattr(model, 'named_steps'):
            print("✓ Model has correct pipeline structure")
            
            if 'scaler' in model.named_steps:
                print("✓ Feature scaler found")
            
            if 'classifier' in model.named_steps:
                classifier_type = type(model.named_steps['classifier']).__name__
                print(f"✓ Classifier found: {classifier_type}")
            
            return True
        else:
            print("✗ Model structure incorrect")
            return False
            
    except Exception as e:
        print(f"✗ Model loading error: {e}")
        return False

def test_feature_extraction():
    """Test feature extraction"""
    print("\n=== Testing Feature Extraction ===")
    
    if not os.path.exists('dataset'):
        print("✗ No dataset found for testing.")
        return False
    
    # Find a test image
    test_image = None
    for person in os.listdir('dataset'):
        person_path = os.path.join('dataset', person)
        if os.path.isdir(person_path):
            for img_file in os.listdir(person_path):
                if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    test_image = os.path.join(person_path, img_file)
                    break
            if test_image:
                break
    
    if not test_image:
        print("✗ No test images found.")
        return False
    
    try:
        from Face_Features import extract_features
        features = extract_features(test_image)
        
        if features and len(features) > 0:
            print(f"✓ Feature extraction successful! {len(features)} features extracted")
            return True
        else:
            print("✗ Feature extraction failed - no features returned")
            return False
            
    except Exception as e:
        print(f"✗ Feature extraction error: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 COMPLETE SYSTEM VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Feature Extraction", test_feature_extraction),
        ("Model Training", test_training),
        ("Model Loading", test_model_loading),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED! System is ready to use.")
        print("\nYou can now run: python main.py")
    else:
        print(f"\n❌ {len(results) - passed} tests failed. Please check the errors above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
