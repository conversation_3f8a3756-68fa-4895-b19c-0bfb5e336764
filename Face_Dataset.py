import cv2
import os

def create_dataset(name):
    cam = cv2.VideoCapture(0)
    face_detector = cv2.CascadeClassifier('haarcascade_frontalface_default.xml')
    sampleNum = 0
    dataset_path = f'dataset/{name}'
    os.makedirs(dataset_path, exist_ok=True)

    while True:
        _, img = cam.read()
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        faces = face_detector.detectMultiScale(gray, 1.3, 5)
        for (x, y, w, h) in faces:
            sampleNum += 1
            cv2.imwrite(f"{dataset_path}/{name}_{sampleNum}.jpg", gray[y:y+h, x:x+w])
            cv2.rectangle(img, (x,y), (x+w,y+h), (255,0,0), 2)
        cv2.imshow('Creating Dataset...', img)
        if cv2.waitKey(100) & 0xFF == ord('q') or sampleNum >= 50:
            break

    cam.release()
    cv2.destroyAllWindows()
