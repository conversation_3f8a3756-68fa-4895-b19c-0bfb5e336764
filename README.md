# Face Recognition System

A complete face recognition system using OpenCV, scikit-learn, and advanced feature extraction (LBP + Histogram) for accurate face detection and recognition.

## Features

- **Face Dataset Creation**: Capture and store face images for training
- **Advanced Feature Extraction**: Extract LBP (Local Binary Pattern) and histogram features for better face recognition
- **Machine Learning Model**: Train an SVM classifier with feature scaling for improved accuracy
- **Real-time Recognition**: Live face recognition with webcam
- **Email Alerts**: Send security alerts for unauthorized access

## Files Description

- `main.py` - Main application entry point
- `Face_Dataset.py` - Creates face datasets by capturing images from webcam
- `Face_Features.py` - Extracts LBP and histogram features from face images (improved)
- `GLCM.py` - Legacy GLCM feature extraction (kept for compatibility)
- `Recognition_Model.py` - Trains the SVM classifier model with feature scaling
- `Email_Alerts.py` - Handles face recognition and email notifications
- `requirements.txt` - Python dependencies
- `haarcascade_frontalface_default.xml` - Haar cascade for face detection

## Installation

1. Install required packages:
```bash
pip install -r requirements.txt
```

2. Run the main application:
```bash
python main.py
```

## Usage

### Quick Start:
```bash
# 1. Run the application
python main.py

# 2. Create datasets (Option 1)
# - Enter person's name
# - Let it capture 50 images
# - Repeat for each person

# 3. Train model (Option 2)
# - Automatically detects single vs multiple people
# - Uses appropriate classifier and thresholds

# 4. Start recognition (Option 3)
# - Real-time face recognition with confidence scores
# - Press 'q' to quit
```

### System Behavior:
- **Single Person Dataset**: Uses KNN classifier with lenient thresholds (70% confidence)
- **Multiple People Dataset**: Uses SVM classifier with strict thresholds (85% confidence)
- **Visual Feedback**: Green (authorized), Yellow (uncertain), Red (unauthorized)

## Configuration

To enable email alerts, update the email credentials in `Email_Alerts.py`:
- Replace `<EMAIL>` with your Gmail address
- Replace `your_password` with your Gmail app password
- Replace `<EMAIL>` with the recipient's email

## Requirements

- Python 3.7+
- Webcam for face capture and recognition
- Internet connection for email alerts

## Note

Make sure your webcam is properly connected and accessible before running the application.
