"""
Face Recognition System - Main Application
This script demonstrates how to use the face recognition system.
"""

from Face_Dataset import create_dataset
from Recognition_Model import train_model
from Email_Alerts import recognize_face
import os

def main():
    """Main function to run the face recognition system."""
    print("=== Face Recognition System ===")
    print("1. Create Dataset")
    print("2. Train Model")
    print("3. Start Recognition")
    print("4. Exit")
    
    while True:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            name = input("Enter person's name for dataset creation: ").strip()
            if name:
                print(f"Creating dataset for {name}...")
                print("Press 'q' to stop capturing images.")
                create_dataset(name)
                print(f"Dataset created for {name}")
            else:
                print("Please enter a valid name.")
                
        elif choice == '2':
            if os.path.exists('dataset') and os.listdir('dataset'):
                print("Training model...")
                train_model()
                print("Model trained and saved as 'face_recognition_model.pkl'")
            else:
                print("No dataset found. Please create a dataset first.")
                
        elif choice == '3':
            if os.path.exists('face_recognition_model.pkl'):
                print("Starting face recognition...")
                print("Press 'q' to stop recognition.")
                recognize_face()
            else:
                print("No trained model found. Please train the model first.")
                
        elif choice == '4':
            print("Exiting...")
            break
            
        else:
            print("Invalid choice. Please enter 1-4.")

if __name__ == "__main__":
    main()
