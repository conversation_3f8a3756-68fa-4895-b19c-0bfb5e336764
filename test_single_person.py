"""
Test script for single person dataset
"""

import os
from Recognition_Model import train_model

def test_single_person_training():
    """Test if training works with single person dataset"""
    
    print("=== Testing Single Person Dataset Training ===")
    
    # Check if we have a dataset
    if not os.path.exists('dataset'):
        print("No dataset found. Please create a dataset first.")
        return False
    
    # Count people in dataset
    people = [d for d in os.listdir('dataset') if os.path.isdir(os.path.join('dataset', d))]
    print(f"Found {len(people)} people in dataset: {people}")
    
    if len(people) == 0:
        print("No people found in dataset.")
        return False
    
    # Try training
    try:
        print("Attempting to train model...")
        success = train_model()
        
        if success:
            print("✓ Training successful!")
            
            # Check if model file was created
            if os.path.exists('face_recognition_model.pkl'):
                print("✓ Model file created successfully!")
                return True
            else:
                print("✗ Model file not found after training")
                return False
        else:
            print("✗ Training failed")
            return False
            
    except Exception as e:
        print(f"✗ Training error: {e}")
        return False

if __name__ == "__main__":
    success = test_single_person_training()
    
    if success:
        print("\n🎉 SUCCESS: Single person training works!")
        print("You can now use option 3 to test recognition.")
    else:
        print("\n❌ FAILED: Single person training failed.")
        print("Please check your dataset and try again.")
