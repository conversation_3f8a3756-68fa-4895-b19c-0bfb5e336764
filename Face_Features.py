"""
Improved Face Feature Extraction using multiple methods
This module provides better face recognition features than GLCM
"""

import cv2
import numpy as np
from skimage.feature import local_binary_pattern

def extract_lbp_features(image_path, radius=3, n_points=24):
    """
    Extract Local Binary Pattern features from face image
    LBP is much better for face recognition than GLCM
    """
    try:
        # Read image
        img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            return None

        # Resize to standard size
        img = cv2.resize(img, (100, 100))

        # Apply histogram equalization for better contrast
        img = cv2.equalizeHist(img)

        # Extract LBP features
        lbp = local_binary_pattern(img, n_points, radius, method='uniform')

        # Calculate histogram of LBP
        hist, _ = np.histogram(lbp.ravel(), bins=n_points + 2, range=(0, n_points + 2))

        # Normalize histogram
        hist = hist.astype(float)
        hist /= (hist.sum() + 1e-7)

        return hist.tolist()
    except Exception as e:
        print(f"Error extracting LBP features from {image_path}: {e}")
        return None

def extract_histogram_features(image_path):
    """
    Extract histogram-based features from face image
    This provides better discrimination than GLCM
    """
    try:
        # Read image
        img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            return None

        # Resize to standard size
        img = cv2.resize(img, (100, 100))

        # Apply histogram equalization
        img = cv2.equalizeHist(img)

        # Calculate histogram
        hist = cv2.calcHist([img], [0], None, [256], [0, 256])

        # Normalize histogram
        hist = hist.flatten()
        hist = hist / (np.sum(hist) + 1e-7)

        # Take only the most significant bins to reduce dimensionality
        # Use every 4th bin to get 64 features instead of 256
        reduced_hist = hist[::4]

        return reduced_hist.tolist()
    except Exception as e:
        print(f"Error extracting histogram features from {image_path}: {e}")
        return None

def extract_combined_features(image_path):
    """
    Extract combined features using both LBP and basic image statistics
    This provides a robust feature set for face recognition
    """
    try:
        # Read image
        img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            return None

        # Resize to standard size
        img = cv2.resize(img, (100, 100))

        # Apply histogram equalization
        img = cv2.equalizeHist(img)

        # Extract LBP features
        lbp_features = extract_lbp_features(image_path)
        if lbp_features is None:
            return None

        # Extract statistical features
        mean_val = np.mean(img)
        std_val = np.std(img)

        # Extract edge features using Canny
        edges = cv2.Canny(img, 50, 150)
        edge_density = np.sum(edges > 0) / (img.shape[0] * img.shape[1])

        # Combine all features
        combined_features = lbp_features + [mean_val, std_val, edge_density]

        return combined_features
    except Exception as e:
        print(f"Error extracting combined features from {image_path}: {e}")
        return None

# For backward compatibility, use the improved method as default
def extract_features(image_path):
    """
    Main feature extraction function - uses combined LBP and histogram features
    for better face recognition than GLCM
    """
    # Try LBP features first (good for face recognition)
    lbp_features = extract_lbp_features(image_path)
    if lbp_features is not None:
        # Add histogram features for better discrimination
        hist_features = extract_histogram_features(image_path)
        if hist_features is not None:
            # Combine both feature types
            combined = lbp_features + hist_features
            return combined
        else:
            # Use only LBP if histogram fails
            return lbp_features

    # Fall back to combined features if LBP fails
    features = extract_combined_features(image_path)
    if features is not None:
        return features

    # If all else fails, return None
    print(f"Could not extract any features from {image_path}")
    return None
