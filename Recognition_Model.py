from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
import joblib
import os
import numpy as np
from Face_Features import extract_features

def train_model():
    """
    Train an improved face recognition model with better preprocessing
    """
    X, y = [], []
    base_path = "dataset/"

    print("Extracting features from training images...")

    # Extract features from all training images
    for person in os.listdir(base_path):
        person_path = os.path.join(base_path, person)
        if not os.path.isdir(person_path):
            continue

        print(f"Processing {person}...")
        person_features = []

        for img_file in os.listdir(person_path):
            if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(person_path, img_file)
                features = extract_features(img_path)

                if features is not None:
                    X.append(features)
                    y.append(person)
                    person_features.append(features)
                else:
                    print(f"Warning: Could not extract features from {img_path}")

        print(f"Extracted features from {len(person_features)} images for {person}")

    if len(X) == 0:
        print("Error: No features extracted from any images!")
        return False

    # Convert to numpy arrays
    X = np.array(X)
    y = np.array(y)

    print(f"Training with {len(X)} samples from {len(set(y))} people")
    print(f"Feature dimension: {X.shape[1]}")

    # Check if we have enough classes for SVM
    num_classes = len(set(y))

    if num_classes < 2:
        print("Warning: Only 1 person in dataset. Using KNN classifier instead of SVM.")
        print("For better accuracy, add more people to your dataset.")

        # Use KNN for single class (more forgiving)
        from sklearn.neighbors import KNeighborsClassifier
        model = Pipeline([
            ('scaler', StandardScaler()),
            ('classifier', KNeighborsClassifier(n_neighbors=min(3, len(X)), weights='distance'))
        ])
    else:
        print("Multiple people detected. Using SVM classifier for better accuracy.")
        # Create a pipeline with scaling and classification
        # Use SVM for better performance with face recognition
        model = Pipeline([
            ('scaler', StandardScaler()),
            ('classifier', SVC(kernel='rbf', probability=True, C=1.0, gamma='scale'))
        ])

    # Train the model
    model.fit(X, y)

    # Save the model
    joblib.dump(model, 'face_recognition_model.pkl')
    print("Model trained and saved successfully!")

    return True
