import joblib
import cv2
import numpy as np
from Face_Features import extract_features

def recognize_face():
    """
    Improved face recognition with better threshold and confidence scoring
    """
    try:
        model = joblib.load('face_recognition_model.pkl')
    except FileNotFoundError:
        print("Error: Model file not found. Please train the model first.")
        return

    cam = cv2.VideoCapture(0)
    face_detector = cv2.CascadeClassifier('haarcascade_frontalface_default.xml')

    # Check what type of model we have and adjust thresholds accordingly
    try:
        # Try to determine if it's a single-person model
        temp_model = joblib.load('face_recognition_model.pkl')

        # Check if it's KNN (single person) or SVM (multiple people)
        classifier_type = type(temp_model.named_steps['classifier']).__name__

        if 'KNeighbors' in classifier_type:
            # Single person dataset - use more lenient thresholds
            CONFIDENCE_THRESHOLD = 0.70  # Lower threshold for single person
            UNKNOWN_THRESHOLD = 0.40     # More lenient for single person
            print("Single-person model detected. Using adjusted thresholds.")
        else:
            # Multiple people dataset - use strict thresholds
            CONFIDENCE_THRESHOLD = 0.85  # Higher threshold for better accuracy
            UNKNOWN_THRESHOLD = 0.60     # If below this, definitely unknown
            print("Multi-person model detected. Using strict thresholds.")

    except:
        # Default thresholds if we can't determine model type
        CONFIDENCE_THRESHOLD = 0.75
        UNKNOWN_THRESHOLD = 0.50

    print("Face recognition started. Press 'q' to quit.")

    while True:
        _, img = cam.read()
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        faces = face_detector.detectMultiScale(gray, 1.3, 5)

        for (x, y, w, h) in faces:
            # Extract face region
            roi = gray[y:y+h, x:x+w]

            # Save temporary image for feature extraction
            cv2.imwrite("temp.jpg", roi)

            # Extract features
            features = extract_features("temp.jpg")

            if features is not None:
                # Get prediction and probabilities
                prediction = model.predict([features])
                probabilities = model.predict_proba([features])[0]
                max_probability = np.max(probabilities)
                predicted_name = prediction[0]

                # Improved decision logic
                if max_probability >= CONFIDENCE_THRESHOLD:
                    # High confidence - grant access
                    cv2.putText(img, f"Access Granted: {predicted_name}",
                              (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,0), 2)
                    cv2.putText(img, f"Confidence: {max_probability:.2f}",
                              (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,0), 1)
                    cv2.rectangle(img, (x,y), (x+w,y+h), (0,255,0), 2)

                elif max_probability >= UNKNOWN_THRESHOLD:
                    # Medium confidence - show as uncertain
                    cv2.putText(img, f"Uncertain: {predicted_name}?",
                              (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,255), 2)
                    cv2.putText(img, f"Confidence: {max_probability:.2f}",
                              (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,255), 1)
                    cv2.rectangle(img, (x,y), (x+w,y+h), (0,255,255), 2)

                else:
                    # Low confidence - unauthorized
                    cv2.putText(img, "Unauthorized!",
                              (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)
                    cv2.putText(img, f"Confidence: {max_probability:.2f}",
                              (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,0,255), 1)
                    cv2.rectangle(img, (x,y), (x+w,y+h), (0,0,255), 2)

                    # Log unauthorized access
                    print("⚠️ Security Alert: Unknown face detected!")
            else:
                # Could not extract features
                cv2.putText(img, "Face not clear",
                          (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (128,128,128), 2)
                cv2.rectangle(img, (x,y), (x+w,y+h), (128,128,128), 2)

        cv2.imshow('Face Recognition System', img)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cam.release()
    cv2.destroyAllWindows()
    print("Face recognition stopped.")
