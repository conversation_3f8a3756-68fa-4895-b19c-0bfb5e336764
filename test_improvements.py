"""
Test script to verify the improvements in face recognition
"""

import os
import numpy as np
from Face_Features import extract_features
from GLCM import extract_features as extract_glcm_features

def test_feature_extraction():
    """Test if the new feature extraction works better than GLCM"""

    print("=== Testing Feature Extraction Improvements ===")

    # Check if we have any test images
    if not os.path.exists('dataset'):
        print("No dataset found. Please create a dataset first to test.")
        return

    # Find a test image
    test_image = None
    for person in os.listdir('dataset'):
        person_path = os.path.join('dataset', person)
        if os.path.isdir(person_path):
            for img_file in os.listdir(person_path):
                if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    test_image = os.path.join(person_path, img_file)
                    break
            if test_image:
                break

    if not test_image:
        print("No test images found in dataset.")
        return

    print(f"Testing with image: {test_image}")

    # Test old GLCM features
    print("\n1. Testing GLCM features (old method):")
    try:
        glcm_features = extract_glcm_features(test_image)
        if glcm_features:
            print(f"   ✓ GLCM features extracted: {len(glcm_features)} dimensions")
            print(f"   Sample values: {glcm_features[:3]}")
        else:
            print("   ✗ GLCM feature extraction failed")
    except Exception as e:
        print(f"   ✗ GLCM error: {e}")

    # Test new improved features
    print("\n2. Testing improved features (new method):")
    try:
        new_features = extract_features(test_image)
        if new_features:
            print(f"   ✓ Improved features extracted: {len(new_features)} dimensions")
            print(f"   Sample values: {new_features[:3]}")

            # Check feature diversity (important for discrimination)
            feature_std = np.std(new_features)
            print(f"   Feature diversity (std): {feature_std:.4f}")

            if len(new_features) > len(glcm_features if 'glcm_features' in locals() and glcm_features else []):
                print("   ✓ More features than GLCM (better for discrimination)")

        else:
            print("   ✗ Improved feature extraction failed")
    except Exception as e:
        print(f"   ✗ Improved features error: {e}")

    print("\n=== Feature Extraction Test Complete ===")

def test_model_compatibility():
    """Test if the model training works with new features"""

    print("\n=== Testing Model Training Compatibility ===")

    try:
        from Recognition_Model import train_model

        if os.path.exists('dataset') and any(os.listdir('dataset')):
            print("Dataset found. Testing model training...")

            # Backup existing model if it exists
            if os.path.exists('face_recognition_model.pkl'):
                os.rename('face_recognition_model.pkl', 'face_recognition_model_backup.pkl')
                print("Backed up existing model")

            # Test training
            success = train_model()

            if success:
                print("✓ Model training successful with improved features!")

                # Test model loading
                import joblib
                _ = joblib.load('face_recognition_model.pkl')
                print("✓ Model loading successful!")

                # Restore backup if needed
                if os.path.exists('face_recognition_model_backup.pkl'):
                    os.remove('face_recognition_model_backup.pkl')
                    print("Removed backup model")

            else:
                print("✗ Model training failed")

                # Restore backup if training failed
                if os.path.exists('face_recognition_model_backup.pkl'):
                    os.rename('face_recognition_model_backup.pkl', 'face_recognition_model.pkl')
                    print("Restored backup model")
        else:
            print("No dataset found for training test")

    except Exception as e:
        print(f"✗ Model training test error: {e}")

    print("=== Model Training Test Complete ===")

if __name__ == "__main__":
    test_feature_extraction()
    test_model_compatibility()

    print("\n" + "="*50)
    print("SUMMARY OF IMPROVEMENTS:")
    print("1. ✓ Replaced GLCM with LBP + Histogram features")
    print("2. ✓ Added SVM classifier with feature scaling")
    print("3. ✓ Improved confidence thresholds (85% for access)")
    print("4. ✓ Better error handling and user feedback")
    print("5. ✓ More robust feature extraction pipeline")
    print("="*50)
